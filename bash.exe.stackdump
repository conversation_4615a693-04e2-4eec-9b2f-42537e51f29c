Stack trace:
Frame         Function      Args
0007FFFFBE80  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBE80, 0007FFFFAD80) msys-2.0.dll+0x1FEBA
0007FFFFBE80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC158) msys-2.0.dll+0x67F9
0007FFFFBE80  000210046832 (000210285FF9, 0007FFFFBD38, 0007FFFFBE80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBE80  0002100690B4 (0007FFFFBE90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFC160  00021006A49D (0007FFFFBE90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE40690000 ntdll.dll
7FFE3FF10000 KERNEL32.DLL
7FFE3D5F0000 KERNELBASE.dll
7FFE3E780000 USER32.dll
7FFE3CE80000 win32u.dll
7FFE3DAC0000 GDI32.dll
7FFE3D2D0000 gdi32full.dll
7FFE3D030000 msvcp_win.dll
7FFE3CF30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE3E6B0000 advapi32.dll
7FFE3E540000 msvcrt.dll
7FFE3DDD0000 sechost.dll
7FFE3DF50000 RPCRT4.dll
7FFE3CE50000 bcrypt.dll
7FFE3BFF0000 CRYPTBASE.DLL
7FFE3CEA0000 bcryptPrimitives.dll
7FFE3E510000 IMM32.DLL
